import Empty from "@/components/blocks/empty";
import { Generation4o, get4oGenerationsByUserUuid } from "@/models/generation";
import { getTranslations, getLocale } from "next-intl/server";
import { getUserUuid } from "@/services/user";
import moment from "moment";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

export const runtime = "edge";

export default async function ({ searchParams }: { searchParams: { page?: string } }) {
  const t = await getTranslations();
  const locale = await getLocale();
  const pageNumber = searchParams?.page ? parseInt(searchParams.page) : 1;
  const itemsPerPage = 9;
  
  const user_uuid = await getUserUuid();
  
  if (!user_uuid) {
    return <Empty message="no auth" />;
  }
  
  const generations4o = await get4oGenerationsByUserUuid(user_uuid, pageNumber, itemsPerPage);
  
  // Debug the generations array
  console.log("Generations data:", JSON.stringify(generations4o));
  
  if (!generations4o || generations4o.length === 0) {
    return (
      <div className="container max-w-6xl mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">{t("my_images.title") || "My Generated Images"}</h1>
        <div className="bg-muted/30 rounded-lg p-10 text-center">
          <p className="text-muted-foreground">
            {t("my_images.no_images") || "You haven't generated any images yet."}
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container max-w-6xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">{t("my_images.title") || "My Generated Images"}</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {generations4o.map((generation) => (
          <FourOGenerationCard key={generation.id} generation={generation} t={t} />
        ))}
      </div>
      
      <div className="flex justify-center items-center mt-8 gap-2">
        {pageNumber > 1 && (
          <Link href={`/my-images?page=${pageNumber - 1}`}>
            <Button variant="outline" size="sm">
              <ChevronLeft className="h-4 w-4 mr-2" />
              {t("pagination.previous")}
            </Button>
          </Link>
        )}
        
        <span className="mx-2 text-muted-foreground">
          {locale === 'zh' 
            ? `${t("pagination.page")} ${pageNumber} 页` 
            : `${t("pagination.page")} ${pageNumber}`}
        </span>
        
        {generations4o.length === itemsPerPage && (
          <Link href={`/my-images?page=${pageNumber + 1}`}>
            <Button variant="outline" size="sm">
              {t("pagination.next")}
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        )}
      </div>
    </div>
  );
}

function FourOGenerationCard({ generation, t }: { generation: Generation4o, t: any }) {
  // Debug each generation object
  console.log("Generation object:", JSON.stringify(generation));
  console.log("Image URL type:", typeof generation.generated_image_url);
  console.log("Image URL value:", generation.generated_image_url);

  // Check if the URL is valid - handle null, empty string, "{}", or invalid URLs
  const isValidUrl = (url: any): boolean => {
    if (!url || url === null || url === undefined) return false;
    if (typeof url !== 'string') return false;
    if (url.trim() === '' || url === '{}') return false;

    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const hasValidImage = isValidUrl(generation.generated_image_url);

  if (!hasValidImage) {
    return (
      <div className="bg-card border rounded-lg overflow-hidden shadow-sm">
        <div className="relative aspect-square bg-muted flex items-center justify-center">
          <div className="text-center p-4">
            <div className="text-sm font-medium">{generation.status === 'pending' ? 'Processing...' : 'Failed'}</div>
          </div>
        </div>

        <div className="p-4">
          <p className="text-sm text-muted-foreground truncate" title={generation.prompt}>
            {generation.prompt}
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            {t("my_images.created_at") || "Created at"}: {moment(generation.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </p>
        </div>
      </div>
    );
  }

  const imageUrl = generation.generated_image_url as string;

  return (
    <div className="bg-card border rounded-lg overflow-hidden shadow-sm">
      <a href={imageUrl} target="_blank" rel="noopener noreferrer" className="block relative aspect-square">
        <Image
          src={imageUrl}
          alt="Generated image"
          fill
          className="object-cover transition-transform hover:scale-105"
        />
      </a>

      <div className="p-4">

        <p className="text-sm text-muted-foreground mt-2">
          {t("my_images.created_at") || "Created at"}: {moment(generation.created_at).format("YYYY-MM-DD HH:mm:ss")}
        </p>
        <p className="text-sm text-muted-foreground">
          Status: {generation.status}
        </p>
      </div>
    </div>
  );
}