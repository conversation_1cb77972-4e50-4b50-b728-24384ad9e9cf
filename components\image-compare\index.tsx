"use client";

import React from 'react';
import Image from 'next/image';
import { cn } from "@/lib/utils";

export interface ImageCompareProps
  extends React.HTMLAttributes<HTMLDivElement> {
  originalSrc: string;
  modifiedSrc: string;
  alt: string;
  beforeText?: string;
  afterText?: string;
}

export const ImageCompare = ({
  originalSrc,
  modifiedSrc,
  alt,
  beforeText = "Before",
  afterText = "After",
  className,
  ...props
}: ImageCompareProps) => {
  return (
    <div className={cn("w-full max-w-[1200px] mx-auto space-y-6 md:space-y-0 md:grid md:grid-cols-2 md:gap-8", className)} {...props}>
      {/* Original Image */}
      <div className="relative w-full aspect-square rounded-2xl overflow-hidden shadow-lg group">
        <Image 
          src={originalSrc} 
          alt={`Restore Old Photos transforms photo - ${alt}`} 
          fill 
          className="object-cover transition duration-300 ease-in-out group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
          priority
          quality={95}
        />
        <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <span className="text-white text-sm md:text-base font-medium">{beforeText}</span>
        </div>
      </div>

      {/* Modified Image */}
      <div className="relative w-full aspect-square rounded-2xl overflow-hidden shadow-lg group">
        <Image 
          src={modifiedSrc} 
          alt={`Restored photo - ${alt}`} 
          fill 
          className="object-cover transition duration-300 ease-in-out group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
          priority
          quality={95}
        />
        <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <span className="text-white text-sm md:text-base font-medium">{afterText}</span>
        </div>
      </div>
    </div>
  );
}; 